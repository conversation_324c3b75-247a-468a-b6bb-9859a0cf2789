#!/usr/bin/env python3
"""
Automated Prediction Pipeline
Automatically loads ticker data, creates features, loads prediction model,
converts to scores using ScoreMapperSimple, and saves results with multiprocessing.
"""

import os
import sys
import json
import re
import pandas as pd
import numpy as np
import joblib
import xgboost as xgb
from datetime import timedel<PERSON>
from pathlib import Path
from pathos.multiprocessing import ProcessingPool as Pool
import traceback
import warnings
from collections import Counter
warnings.filterwarnings('ignore')

# Setup paths
current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/deeplearning", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

# Import existing modules
from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST
from core_utils.redis_cache import EvalRedis
from joblib import Memory

# Configuration
TICKER_PATH = 'ticker_v1a/'
MODEL_PATH = 'deeplearning/outputs/models/'
OUTPUT_PATH = 'deeplearning/predictions/'
NUM_PROCESSES = 20

# Initialize cache
memory = Memory(location=f'{JOBLIB_CACHE_DIR}_prediction', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)

# Create output directory
Path(OUTPUT_PATH).mkdir(parents=True, exist_ok=True)


def _logit(x): 
    return np.log(x) - np.log(1 - x)


def _sigmoid(x): 
    return 1 / (1 + np.exp(-x))


class ScoreMapperSimple:
    """
    Score mapper that converts probabilities to scores in [-1, 3] range.
    Copied from exp_ml_dl.ipynb with modifications for production use.
    """
    
    def __init__(self, score_knots=None, use_temperature=False):
        self.score_knots = np.array([-1.0, -0.4, 1.0, 1.8, 2.95, 3.0]) if score_knots is None else np.asarray(score_knots, float)
        self.use_temperature = use_temperature
        self.t_ = 1.0  # temperature parameter
        self.percentiles_ = None
        
    def _apply_temp(self, p):
        if not self.use_temperature:
            return p
        p = np.clip(np.asarray(p, float), 1e-9, 1 - 1e-9)
        return _sigmoid(self.t_ * _logit(p))
    
    def fit(self, p_valid_raw, y_valid=None, lift_target_rates=None, base_rate=None):
        """Fit the score mapper on validation probabilities."""
        p_valid_raw = np.asarray(p_valid_raw, float)
        
        # Learn temperature if specified
        if self.use_temperature and y_valid is not None:
            y = np.asarray(y_valid, float)
            p = np.clip(p_valid_raw, 1e-9, 1 - 1e-9)
            z = _logit(p)
            ts = np.linspace(0.5, 3.0, 26)
            best = (np.inf, 1.0)
            for t in ts:
                q = _sigmoid(t * z)
                brier = np.mean((q - y) ** 2)
                if brier < best[0]:
                    best = (brier, t)
            self.t_ = best[1]
        
        # Auto adjust knots based on lift table if provided
        if lift_target_rates is not None and base_rate is not None:
            tr = np.asarray(lift_target_rates, float) / float(base_rate)
            def safe_g(num, den): 
                return num / den if abs(den) > 1e-12 else np.inf
            g_lo = safe_g(-2.0, tr[0] - 1.0)
            g_hi = safe_g(2.0, tr[-1] - 1.0)
            g = min([g for g in (g_lo, g_hi) if g > 0] or [2.0])
            s5 = 1.0 + g * (tr - 1.0)
            s5[2] = 1.0
            pct5 = np.array([10, 30, 50, 70, 90])
            pct6 = np.array([0, 20, 40, 60, 80, 100])
            s6 = np.interp(pct6, pct5, s5, left=s5[0], right=s5[-1])
            s6 = np.clip(s6, -1.0, 3.0)
            s6[2] = 1.0
            self.score_knots = s6
        
        # Get percentiles on processed probabilities
        p_use = self._apply_temp(p_valid_raw)
        self.percentiles_ = np.quantile(p_use, [0, .2, .4, .6, .8, 1.0])
        return self
    
    def transform(self, p_raw, clip=True):
        """Transform probabilities to scores."""
        assert self.percentiles_ is not None, "Must fit() before transform()"
        p_use = self._apply_temp(p_raw)
        s = np.interp(p_use, self.percentiles_, self.score_knots)
        if clip: 
            s = np.clip(s, -1.0, 3.0)
        return s


def safe_eval_formula(row, formula):
    """Safely evaluate formula expressions for feature engineering."""
    try:
        python_funcs = {'abs', 'min', 'max', 'round', 'sum', 'np', 'float', 'int'}
        vars_in_formula = set(re.findall(r'[A-Za-z_][A-Za-z0-9_]*', formula))
        expr = formula
        for var in vars_in_formula:
            if var not in python_funcs and var in row:
                expr = re.sub(rf'\b{var}\b', f'row["{var}"]', expr)
        return eval(expr, {'np': np, 'abs': abs, 'min': min, 'max': max, 'sum': sum, 'round': round, 'float': float, 'int': int}, {'row': row})
    except ZeroDivisionError:
        return 0
    except Exception:
        return np.nan


def enrich_with_indicators(df, full_expr):
    """Add computed indicator columns to dataframe."""
    df = df.copy()
    all_full_expr = [expr for expr in full_expr if expr not in df.columns]
    df[all_full_expr] = np.nan
    for formula in full_expr:
        if formula not in df.columns:
            df[formula] = df.apply(lambda row: safe_eval_formula(row, formula), axis=1)
    return df


def fill_data(df):
    """Fill missing fundamental data using forward fill."""
    fundamental_cols = [
        'PB_SD1Y', 'PB_MA1Y', 'PE_SD1Y', 'PE_MA1Y', 'PB_SD5Y', 'PB_MA5Y', 'PE_SD5Y', 'PE_MA5Y', 'PB', 'BVPS', 'PE',
        'PCF', 'OShares', 'LtDebt_P0', 'CF_OA_P0', 'CF_OA_P1', 'CF_OA_P2', 'CF_OA_P3', 'CF_Invest_P0', 'CF_Invest_P1',
        'CF_Invest_P2', 'CF_Invest_P3', 'NP_P0', 'NP_P1', 'NP_P2', 'NP_P3', 'NP_P4'
    ]
    
    FFILL_LIMIT_QUARTER = 66
    for col in fundamental_cols:
        if col in df.columns:
            df.loc[:, col] = df[col].ffill(limit=FFILL_LIMIT_QUARTER)
    
    fill_0_cols = ['Volume']
    for col in fill_0_cols:
        if col in df.columns:
            df.loc[:, col] = df[col].fillna(0)


def preprocess_ticker_data(df):
    """Preprocess ticker data by removing low volume periods."""
    df['zero_volume'] = (df['Volume'] < 1000).astype(int)
    df['zero_streak'] = df['zero_volume'].rolling(5).sum()
    df = df[df['zero_streak'] < 5]
    df = df.drop(['zero_volume', 'zero_streak'], axis=1)
    return df


def parse_formula_from_filter(dict_filter):
    """Parse formulas from filter dictionary to extract feature columns."""
    def extract_simple_thresholds(dict_filter):
        dict_filter = {k: v for k, v in dict_filter.items() if (k.startswith('_') or k.startswith('~'))}
        simple_formula = []
        pattern = r'\(\s*([a-zA-Z_]\w*)\s*(==|!=|>=|<=|>|<)\s*([-+]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)\s*\)'

        for key, value in dict_filter.items():
            value = re.sub(r'\b[a-zA-Z_]\w*\s*\([^()]*\)', '', value)
            matches = re.findall(pattern, value)
            for A, op, num in matches:
                simple_formula.append(A)

        final_indicators = []
        remove_indicators = {'Price', 'Inflation_7', 'Close', 'Open', 'OShares', 'BVPS'}
        prefixes = ('MA', 'Inflation', 'VAP', 'Sup', 'Res', 'NP', 'ID')
        exclude_substrings = ('Close', 'Volume', 'ID', 'Invest', 'CF_OA', 'HI', 'LO', 'Inflation')

        for indi in simple_formula:
            if indi in remove_indicators:
                continue
            if indi.startswith(prefixes):
                continue
            if any(sub in indi for sub in exclude_substrings):
                continue
            final_indicators.append(indi)

        indicators = set(indi.replace(' ', '') for indi in final_indicators)
        return indicators

    def all_indicators():
        init_cols = ['time', 'ticker', 'EVEB_MA5Y', 'EVEB_MA1Y', 'EVEB_MA3M', 'EVEB_SD5Y', 'EVEB_SD1Y', 'EVEB_SD3M',
                     'EVEB', 'ROIC_Trailing', 'ROIC3Y', 'ROIC5Y', 'ROIC_Min3Y', 'ROIC_Min5Y', 'ROA_P0/ROA_P4',
                     'EBITM_P0/EBITM_P4', 'NPM_P0/NPM_P4', 'CashR_P0/CashR_P4', 'QuickR_P0/QuickR_P4',
                     'FinLev_P0/FinLev_P4', 'AssetTurn_P0/AssetTurn_P4', 'CR_P0/CR_P4', 'FAssetTurn_P0/FAssetTurn_P4',
                     'DSO_P0/DSO_P4', 'DIO_P0/DIO_P4', 'DPO_P0/DPO_P4', 'CashCycle_P0/CashCycle_P4',
                     'InvTurn_P0/InvTurn_P4', 'STLTDebt_Eq_P0/STLTDebt_Eq_P4', 'Debt_Eq_P0/Debt_Eq_P4',
                     'FAsset_Eq_P0/FAsset_Eq_P4', 'OwnEq_Cap_P0/OwnEq_Cap_P4', 'Revenue_YoY_P0',
                     'PB_MA5Y', 'PE_MA5Y', 'PE_SD5Y', 'PB_SD5Y', 'PB_SD1Y', 'PB_MA1Y', 'PE_SD1Y', 'PE_MA1Y',
                     'Volume*Price / Trading_Session', 'Risk_Rating', 'VNINDEX_PE', 'Volume_1M_P50*Price/Inflation_7']

        indicators = set(indi.replace(' ', '') for indi in init_cols)
        return indicators

    def all_formulas(dict_filter):
        dict_filter = {k: v for k, v in dict_filter.items() if (k.startswith('_') or k.startswith('~'))}
        all_filter_expr = json.dumps(dict_filter)

        math_expr_pattern = re.compile(r'''
            (
                (?:
                    [A-Za-z_][A-Za-z0-9_]*
                    (?:\([^\)]*\))?
                    \s*
                    [\/*+\-]
                    \s*
                ){1,}
                [A-Za-z_][A-Za-z0-9_]*
                (?:\([^\)]*\))?
            )
        ''', re.VERBOSE)

        indicator_formulas = set(math_expr_pattern.findall(all_filter_expr))
        indicator_formulas = set(
            re.sub(r'\)+$', '', expr.replace(' ', ''))
            for expr in indicator_formulas
            if not re.match(r'^[\d.eE+\-*/]+$', expr)
        )

        return indicator_formulas

    cols_00 = all_indicators()
    cols_01 = extract_simple_thresholds(dict_filter)
    cols_02 = all_formulas(dict_filter)

    full_expr = cols_00.union(cols_01).union(cols_02)

    final_expr = []
    for col in full_expr:
        ops = re.findall(r'[\+\-\*/]', col)
        counter = Counter(ops)
        total_count = sum(counter.values())
        if total_count > 0:
            if ((counter.get('+', 0) + counter.get('*', 0) + counter.get('-', 0)) == total_count) and not (
                    (counter.get('-', 0) == total_count) and 'ID' in col):
                continue
        final_expr.append(col)

    return sorted(final_expr)


class PredictionPipeline:
    """Main prediction pipeline class."""
    
    def __init__(self, model_path=None, score_mapper_path=None):
        self.model_path = model_path or f"{MODEL_PATH}xgb_model.joblib"
        self.score_mapper_path = score_mapper_path
        self.model = None
        self.score_mapper = None
        self.feature_columns = None
        self.dict_filter = self._get_default_filters()
        
    def _get_default_filters(self):
        """Get default filter dictionary for feature engineering."""
        return {
            "$BKMA200": "BearDvg2, MA21, SellLowGrowth, SellPE, SellResistance, SellResistance1M",
            "$RSILow30": "BearDvg2, MA31, S13, SellBV, SellLowGrowth, SellResistance1M, SellResistance1Y, SellVolMax",
            "$TL3M": "BearDvg2, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M",
            "$BuySupport": "BearDvg2, SellBV, SellLowGrowth, SellResistance, SellResistance1Y",
            "$UnderBV": "BearDvg2, MA21, MA41, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellResistance1Y, SellVolMax",
            "$TrendingGrowth": "BearDvg2, MA41, SellBV, SellBV2, SellLowGrowth, SellPE, SellResistance, SellVolMax",
            "$SuperGrowth": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellResistance, SellResistance1M, SellVolMax",
            "$SurpriseEarning": "BearDvg2, MA21, MA31, MA41, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
            "$Conservative": "MA21, MA41, SellLowGrowth, SellPE, SellResistance, SellResistance1M, SellResistance1Y",
            "$BullDvg": "BearDvg2, MA41, S13, SellBV, SellBV2, SellLowGrowth, SellResistance, SellResistance1Y",
            "$VolMax1Y": "BearDvg2, MA21, MA31, S13, SellBV2, SellLowGrowth, SellPE, SellResistance, SellResistance1Y",
            "Init_s": "(Volume_1M_P50*Price>3e+8*Inflation_7) & (time>='2014-01-01') & (time<='2026-01-01')",
            "Init": "(Volume_1M_P50*Price>1e+9*Inflation_7) & (time>='2014-01-01') & (time<='2026-01-01')",
            "_BKMA200": "{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0 > 1.1*NP_P1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
            "_TrendingGrowth": "{Init} &(Close> 1.0*Volume_Max5Y_High) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
            "_TL3M": "{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
        }
    
    def load_model(self):
        """Load the trained XGBoost model."""
        print(f"Loading model from {self.model_path}")
        self.model = joblib.load(self.model_path)

        self.feature_columns = self.model.get_booster().feature_names
        print("Model loaded successfully")
    
    def setup_score_mapper(self, validation_probs=None, validation_labels=None):
        """Setup the score mapper."""
        self.score_mapper = ScoreMapperSimple()
        if validation_probs is not None:
            self.score_mapper.fit(validation_probs, validation_labels)
        else:
            # Use default percentiles if no validation data
            self.score_mapper.percentiles_ = np.array([0.0, 0.2, 0.4, 0.6, 0.8, 1.0])
        print("Score mapper setup complete")
    
    def get_feature_columns(self):
        """Get the list of feature columns needed for prediction."""
        if self.feature_columns is None:
            # Parse features from filter dictionary
            self.feature_columns = parse_formula_from_filter(self.dict_filter)
        return self.feature_columns
    
    def process_ticker(self, ticker):
        """Process a single ticker and return predictions."""
        try:
            # Load ticker data
            df = pd.read_csv(f'{TICKER_PATH}{ticker}.csv')
            df = df.query("(time >= '2014-01-01') & (time <= '2026-01-01')")

            if len(df) == 0:
                return None

            # Preprocess data
            fill_data(df)
            df = preprocess_ticker_data(df)

            # Remove duplicates and sort
            df = df.drop_duplicates(subset=['time'], keep='last').sort_values(by='time', ascending=True)

            # Feature engineering
            feature_cols = self.get_feature_columns()
            df = enrich_with_indicators(df, feature_cols)

            # Filter to available features that exist in the model
            if hasattr(self.model, 'feature_names_in_'):
                model_features = list(self.model.feature_names_in_)
            else:
                # Use intersection of computed features and what's available
                model_features = [col for col in feature_cols if col in df.columns]

            available_cols = [col for col in model_features if col in df.columns]

            if len(available_cols) == 0:
                print(f"No valid features found for {ticker}")
                return None

            # Prepare features - handle missing values
            X = df[available_cols].fillna(0)

            # Replace infinite values
            X = X.replace([np.inf, -np.inf], np.nan).fillna(0)

            # Make predictions
            print("1")
            probabilities = self.model.predict_proba(X)[:, 1]
            print("2")
            scores = self.score_mapper.transform(probabilities)
            print("3")


            # Prepare output
            result = pd.DataFrame({
                'time': df['time'],
                'ticker': ticker,
                'score': scores
            })

            return result

        except Exception as e:
            print(f"Error processing {ticker}: {e}")
            traceback.print_exc()
            return None
    
    def run_pipeline(self, output_file='predictions.csv'):
        """Run the complete prediction pipeline."""
        print("Starting prediction pipeline...")
        
        # Load model and setup score mapper
        self.load_model()
        self.setup_score_mapper()
        
        # Get list of tickers
        ticker_files = [f for f in os.listdir(TICKER_PATH) if f.endswith('.csv')]
        tickers = [f.replace('.csv', '') for f in ticker_files]
        
        print(f"Processing {len(tickers)} tickers with {NUM_PROCESSES} processes...")
        
        # Process tickers in parallel
        global _pipeline
        _pipeline = self

        with Pool(NUM_PROCESSES) as pool:
            results = pool.map(process_single_ticker, tickers)
        
        # Combine results
        valid_results = [r for r in results if r is not None]
        if valid_results:
            final_df = pd.concat(valid_results, ignore_index=True)
            
            # Save results
            output_path = f"{OUTPUT_PATH}{output_file}"
            final_df.to_csv(output_path, index=False)
            print(f"Predictions saved to {output_path}")
            print(f"Total predictions: {len(final_df)}")
            print(f"Score range: {final_df['score'].min():.3f} to {final_df['score'].max():.3f}")
        else:
            print("No valid predictions generated")


# Global variables for multiprocessing
_pipeline = None

def process_single_ticker(ticker):
    """Process a single ticker - used for multiprocessing."""
    global _pipeline
    return _pipeline.process_ticker(ticker)


def initialize_worker(pipeline):
    """Initialize worker process with pipeline."""
    global _pipeline
    _pipeline = pipeline


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Run prediction pipeline')
    parser.add_argument('--model-path', type=str, help='Path to model file')
    parser.add_argument('--output-file', type=str, default='predictions.csv', help='Output file name')
    parser.add_argument('--num-processes', type=int, default=NUM_PROCESSES, help='Number of processes')

    args = parser.parse_args()

    pipeline = PredictionPipeline(model_path=args.model_path)
    pipeline.run_pipeline(output_file=args.output_file)

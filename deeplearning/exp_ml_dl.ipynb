#%%
import os
import sys
from pathlib import Path

# from tuning.sell.exp_sell import threshold

current_dir = '/home/<USER>/dev/ta/kaffa_v2'
os.chdir(current_dir)
sys.path.insert(0, current_dir)
outdir = Path('deeplearning/outputs')
(outdir / "models").mkdir(parents=True, exist_ok=True)
(outdir / "metrics").mkdir(exist_ok=True)
(outdir / "predictions").mkdir(exist_ok=True)
#%%
# Load the necessary libraries
import random

random.seed(123)
from joblib import Memory
import joblib

from core_utils.constant import JOBLIB_CACHE_DIR, REDIS_HOST

from core_utils.redis_cache import EvalRedis
from datetime import timedelta
import pandas as pd
import seaborn as sns
%matplotlib inline
from sklearn.preprocessing import StandardScaler
import numpy as np
import xgboost as xgb
from sklearn.metrics import classification_report
from sklearn.model_selection import RandomizedSearchCV
from sklearn.utils.class_weight import compute_sample_weight
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
# from ydata_profiling import ProfileReport
from sklearn.metrics import confusion_matrix
# import tensorflow as tf
# print(f'TensorFlow version: {tf.__version__}')
from sklearn.calibration import CalibratedClassifierCV
# from sklearn.utils._estimator_html_repr import FrozenEstimator
import warnings

warnings.simplefilter(action='ignore')
memory = Memory(location=f'{JOBLIB_CACHE_DIR}_tuning', verbose=0)
memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
redis_cache = EvalRedis(host=REDIS_HOST, db=1)

#%%
######2
def proba_to_score_power(p, alpha=0.7, min_score=-1, max_score=3, eps=1e-12):
    """
    Nonlinear mapping: score = min_score + (max_score - min_score) * (p**alpha)
    - alpha < 1: nhấn mạnh positive tail
    - alpha > 1: nhấn mạnh negative tail
    """
    p = np.clip(np.asarray(p, dtype=float), eps, 1 - eps)
    return min_score + (max_score - min_score) * (p ** alpha)


def score_to_proba_power(score, alpha=0.7, min_score=-1, max_score=3):
    score = np.asarray(score, dtype=float)
    normed = (score - min_score) / (max_score - min_score)
    return np.clip(normed ** (1 / alpha), 0, 1)
#%%
import numpy as np


# =========================
# 1) CALIBRATION
# =========================

def _logit(x):
    return np.log(x) - np.log(1 - x)


def _sigmoid(x):
    return 1 / (1 + np.exp(-x))


class Calibrator:
    """
    Ưu tiên Isotonic Regression; nếu thiếu sklearn/scipy thì fallback về Platt.
    """

    def __init__(self, kind="auto"):
        self.kind = kind
        self.model_ = None
        self.ab_ = None

    def fit(self, p_valid, y_valid):
        p = np.clip(np.asarray(p_valid, float), 1e-9, 1 - 1e-9)
        y = np.asarray(y_valid, float)

        if self.kind in ("iso", "auto"):
            try:
                from sklearn.isotonic import IsotonicRegression
                iso = IsotonicRegression(y_min=1e-9, y_max=1 - 1e-9, out_of_bounds='clip')
                iso.fit(p, y)
                self.kind = "iso"
                self.model_ = iso
                return self
            except Exception:
                # rớt xuống Platt
                pass

        # Platt scaling: q = sigmoid(a*p + b)
        try:
            from scipy.optimize import minimize
            def nll(ab):
                a, b = ab
                q = _sigmoid(a * p + b)
                eps = 1e-9
                return -(y * np.log(q + eps) + (1 - y) * np.log(1 - q + eps)).mean()

            res = minimize(nll, x0=[5.0, -2.5], method="L-BFGS-B")
            self.kind = "platt"
            self.ab_ = tuple(res.x)
        except Exception:
            # Fallback cuối: identity (không khuyến nghị)
            self.kind = "identity"
        return self

    def transform(self, p):
        p = np.clip(np.asarray(p, float), 1e-9, 1 - 1e-9)
        if self.kind == "iso" and self.model_ is not None:
            q = self.model_.predict(p)
        elif self.kind == "platt" and self.ab_ is not None:
            a, b = self.ab_
            q = _sigmoid(a * p + b)
        else:
            q = p  # identity fallback
        return np.clip(q, 1e-9, 1 - 1e-9)


# =========================
# 2) SCORE MAPPER (PIECEWISE)
# =========================

def _default_knots():
    """
    Knots theo thứ tự percentiles [0, 20, 40, 60, 80, 100].
    Gợi ý “ăn theo lift” mạnh dần về phải. Bạn có thể override khi fit.
    """
    return np.array([-1.0, -0.4, 1.0, 1.8, 2.95, 3.0], dtype=float)


def derive_knots_from_lift(target_rates, base_rate):
    """
    Auto-fit score_knots từ %target của 5 bin (0–20,...,80–100).
    Ý tưởng: score ≈ 1 + γ*(lift - 1), ép mốc trái ~ -1 và mốc phải ~ 3.
    - target_rates: list/array length=5 (% theo bin), vd [5.17,12.07,16.93,22.07,29.48]
    - base_rate:    vd 17.0
    Trả về: score_knots 6 điểm cho [0,20,40,60,80,100] (mốc trung gian nội suy).
    """
    tr = np.asarray(target_rates, float)
    lifts = tr / float(base_rate)

    # Tính γ để vừa chạm -1 ở bin 0 và ~3 ở bin 4
    # s = 1 + γ*(lift-1)
    # s0 ≈ -1  => γ_low  = (-2)/(lift0-1)
    # s4 ≈  3  => γ_high = ( 2)/(lift4-1)
    def safe_gamma(num, den):
        if abs(den) < 1e-12:
            return np.inf
        return num / den

    gamma_low = safe_gamma(-2.0, lifts[0] - 1.0)
    gamma_high = safe_gamma(2.0, lifts[-1] - 1.0)

    # lấy gamma bé hơn để tránh overshoot
    gamma = min(gamma_low if gamma_low > 0 else np.inf,
                gamma_high if gamma_high > 0 else np.inf)
    if not np.isfinite(gamma):
        gamma = 2.0  # fallback nhẹ

    # 5 nút ứng với 5 bin. Thêm 2 đầu mút để nội suy mượt.
    raw5 = 1.0 + gamma * (lifts - 1.0)
    raw5[2] = 1.0  # giữ bin 40–60 = neutral pivot

    # Nội suy ra 6 mốc (0,20,40,60,80,100). Đầu/cuối lấy gần kề.
    knots5_pct = np.array([10, 30, 50, 70, 90])  # tâm mỗi bin
    pct6 = np.array([0, 20, 40, 60, 80, 100])
    s6 = np.interp(pct6, knots5_pct, raw5, left=raw5[0], right=raw5[-1])

    s6 = np.clip(s6, -1.0, 3.0)
    s6[2] = 1.0  # đảm bảo pivot
    return s6


class ScoreMapper:
    """
    - Fit percentiles trên p_calib (validation).
    - Giữ monotonic mapping: np.interp theo các mốc percentiles -> score_knots.
    """

    def __init__(self, score_knots=None):
        self.score_knots = np.asarray(score_knots, float) if score_knots is not None else _default_knots()
        self.percentiles_ = None  # mốc p-calib tương ứng [0, 20, 40, 60, 80, 100]
        self.calibrator_ = Calibrator(kind="auto")

    def fit(self, p_valid_raw, y_valid, score_knots=None, lift_target_rates=None, base_rate=None):
        # 1) calibrate
        self.calibrator_.fit(p_valid_raw, y_valid)
        p_cal = self.calibrator_.transform(p_valid_raw)

        # 2) xác định score_knots
        if lift_target_rates is not None and base_rate is not None:
            self.score_knots = derive_knots_from_lift(lift_target_rates, base_rate)
        elif score_knots is not None:
            self.score_knots = np.asarray(score_knots, float)
        else:
            self.score_knots = _default_knots()

        # 3) lấy mốc percentiles trên p_cal
        self.percentiles_ = np.quantile(p_cal, [0.0, 0.2, 0.4, 0.6, 0.8, 1.0])
        return self

    def transform(self, p_raw, clip=True):
        # p_raw -> calibrate -> nội suy theo percentiles_ -> score [-1,3]
        assert self.percentiles_ is not None, "Bạn cần fit() trước khi transform()."
        p_cal = self.calibrator_.transform(p_raw)
        s = np.interp(p_cal, self.percentiles_, self.score_knots)
        if clip:
            s = np.clip(s, -1.0, 3.0)
        return s

# =========================
# 3) VÍ DỤ DÙNG NHANH
# =========================
# --- TRAIN / VALID ---
# p_valid_raw = model.predict_proba(X_valid)[:,1]
# y_valid = y_valid_array
# mapper = ScoreMapper()
# mapper.fit(
#     p_valid_raw, y_valid,
#     # Nếu muốn auto-fit theo lift table (ví dụ TEST2 bạn đưa):
#     # lift_target_rates=[5.17, 12.07, 16.93, 22.07, 29.48], base_rate=17.0
# )
#
# --- INFER / PROD ---
# p_test_raw = model.predict_proba(X_test)[:,1]
# score = mapper.transform(p_test_raw)          # trong [-1, 3]
# label = mapper.predict_label(score)           # Negative / Neutral / Positive

#%%
import numpy as np


def _logit(x): return np.log(x) - np.log(1 - x)


def _sigmoid(x): return 1 / (1 + np.exp(-x))


class ScoreMapperSimple:
    """
    Bỏ calibrate mặc định (rank-based). Optional: temperature scaling 1 tham số (nhẹ).
    - Fit percentiles trên proba (hoặc proba sau temperature).
    - Nội suy piecewise-linear sang [-1, 3] theo knots.
    """

    def __init__(self, score_knots=None, use_temperature=False):
        # mốc score cho percentiles [0,20,40,60,80,100]
        self.score_knots = np.array([-1.0, -0.4, 1.0, 1.8, 2.95, 3.0]) if score_knots is None else np.asarray(
            score_knots, float)
        self.use_temperature = use_temperature
        self.t_ = 1.0  # temperature (t>0); t>1 làm proba “gắt” hơn, t<1 làm “mềm” hơn
        self.percentiles_ = None  # các mốc p theo [0,20,40,60,80,100]

    def _apply_temp(self, p):
        if not self.use_temperature:
            return p
        p = np.clip(np.asarray(p, float), 1e-9, 1 - 1e-9)
        return _sigmoid(self.t_ * _logit(p))

    def fit(self, p_valid_raw, y_valid=None, lift_target_rates=None, base_rate=None):
        """
        - p_valid_raw: proba từ XGBoost (validation)
        - y_valid: optional (chỉ dùng khi learn temperature theo Brier)
        - lift_target_rates/base_rate: optional (auto điều chỉnh knots theo lift table)
        """
        p_valid_raw = np.asarray(p_valid_raw, float)

        # Học temperature (optional, 1 tham số)
        if self.use_temperature and y_valid is not None:
            y = np.asarray(y_valid, float)
            p = np.clip(p_valid_raw, 1e-9, 1 - 1e-9)
            z = _logit(p)
            # minimize Brier score w.r.t t (1D search)
            ts = np.linspace(0.5, 3.0, 26)  # nhanh-gọn
            best = (np.inf, 1.0)
            for t in ts:
                q = _sigmoid(t * z)
                brier = np.mean((q - y) ** 2)
                if brier < best[0]:
                    best = (brier, t)
            self.t_ = best[1]

        # Auto chỉnh knots theo lift table (nếu có)
        if lift_target_rates is not None and base_rate is not None:
            tr = np.asarray(lift_target_rates, float) / float(base_rate)  # lifts 5 bin

            # map s ≈ 1 + γ(lift-1), ép trái gần -1, phải gần 3
            def safe_g(num, den): return num / den if abs(den) > 1e-12 else np.inf

            g_lo = safe_g(-2.0, tr[0] - 1.0)
            g_hi = safe_g(2.0, tr[-1] - 1.0)
            g = min([g for g in (g_lo, g_hi) if g > 0] or [2.0])
            s5 = 1.0 + g * (tr - 1.0)
            s5[2] = 1.0  # giữ bin 40–60 = pivot
            # nội suy ra 6 mốc (0,20,40,60,80,100) từ tâm các bin (10,30,50,70,90)
            pct5 = np.array([10, 30, 50, 70, 90])
            pct6 = np.array([0, 20, 40, 60, 80, 100])
            s6 = np.interp(pct6, pct5, s5, left=s5[0], right=s5[-1])
            s6 = np.clip(s6, -1.0, 3.0);
            s6[2] = 1.0
            self.score_knots = s6

        # Lấy percentiles trên p (sau temperature nếu có)
        p_use = self._apply_temp(p_valid_raw)
        self.percentiles_ = np.quantile(p_use, [0, .2, .4, .6, .8, 1.0])
        return self

    def transform(self, p_raw, clip=True):
        assert self.percentiles_ is not None, "Cần fit() trước."
        p_use = self._apply_temp(p_raw)
        s = np.interp(p_use, self.percentiles_, self.score_knots)
        if clip: s = np.clip(s, -1.0, 3.0)
        return s

# mapper = ScoreMapperSimple()
# mapper.fit(p_valid_raw)               # chỉ cần proba VALID để lấy percentiles
# score = mapper.transform(p_test_raw)  # ra [-1, 3]
# label = mapper.predict_label(score)


# mapper = ScoreMapperSimple(use_temperature=True)
# mapper.fit(p_valid_raw, y_valid=y_valid)  # học t bằng Brier
# score = mapper.transform(p_test_raw)


# mapper = ScoreMapperSimple()
# mapper.fit(
#     p_valid_raw,
#     lift_target_rates=[5.17, 12.07, 16.93, 22.07, 29.48],
#     base_rate=17.0
# )
# score = mapper.transform(p_test_raw)
#%%
def show_confusion_matrix(y_true, y_pred_prob, class_names=None, normalize=None, figsize=(6, 6)):
    import numpy as np
    import matplotlib.pyplot as plt
    from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
    if len(y_pred_prob.shape) > 1:
        y_pred = np.argmax(y_pred_prob, axis=1)
    else:
        y_pred = y_pred_prob
    cm = confusion_matrix(y_true, y_pred, normalize=normalize)
    print("Confusion Matrix:")
    print(cm)
    plt.figure(figsize=figsize)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap='Blues', values_format='.2f' if normalize else 'd', ax=plt.gca(), colorbar=False)
    plt.title('Confusion Matrix' + (f' (normalized: {normalize})' if normalize else ''))
    plt.show()


def ks_score(y_true, y_proba):
    from sklearn.metrics import roc_curve
    """
    Tính và hiển thị KS score
    y_true  : Ground truth labels (0 or 1)
    y_proba : Xác suất mô hình dự đoán cho class 1
    KS Score	Mức phân tách	Diễn giải
    0.00–0.20	Rất yếu 😢	Mô hình gần như không phân biệt được class 0 và 1
    0.20–0.40	Trung bình 😐	Mô hình có chút khả năng phân biệt
    0.40–0.70	Tốt 💪	Mô hình phân tách tốt, áp dụng thực tế được
    > 0.70	Rất tốt 🚀	Hiếm khi gặp, cần kiểm tra overfitting
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_proba)
    ks = max(tpr - fpr)
    ks_threshold = thresholds[np.argmax(tpr - fpr)]

    return ks, ks_threshold


def report_lift_table(df_test, y_test, y_pred_proba, profit_col='profit_3M'):
    df_lift = df_test.copy()
    # Thêm label và score
    df_lift['y_true'] = y_test
    df_lift['y_prob'] = y_pred_proba

    df_lift = df_lift.sort_values('y_prob', ascending=False).reset_index(drop=True)

    total_positives = df_lift['y_true'].sum()
    baseline_rate = total_positives / df_lift.shape[0]

    # Thêm profit và ticker nếu có, nếu không thì tạo cột dummy
    if profit_col not in df_lift.columns:
        df_lift[profit_col] = 0
    if 'ticker' not in df_lift.columns:
        df_lift['ticker'] = 'UNK'

    # Binning
    df_lift['bin'] = pd.qcut(df_lift['y_prob'], q=5, labels=['0-20', '20-40', '40-60', '60-80', '80-100'])

    # Group và tính toán
    lift_table = df_lift.groupby('bin', observed=False).agg(
        base=('y_true', 'count'),
        target=('y_true', 'sum'),
        pct_target=('y_true', 'mean'),
        average_Profit=(profit_col, 'mean'),
        ticker_nums=('ticker', pd.Series.nunique)
    ).reset_index()

    lift_table['%target'] = (lift_table['pct_target'] * 100).round(2)
    # lift_table = lift_table.drop('pct_target', axis=1)

    lift_table = lift_table.rename(columns={'bin': 'Bin score', 'average_Profit': 'average Profit'})

    lift_table['lift'] = lift_table['pct_target'] / baseline_rate

    print(lift_table[['Bin score', 'base', 'target', '%target', 'average Profit', 'ticker_nums', 'lift']].to_markdown(
        index=False))


def feature_importance_df(model):
    booster = model.get_booster()
    gain = booster.get_score(importance_type="gain")
    cover = booster.get_score(importance_type="cover")
    weight = booster.get_score(importance_type="weight")
    keys = set(gain.keys()) | set(cover.keys()) | set(weight.keys())
    rows = []
    for k in keys:
        rows.append({
            "feature": k,
            "gain": gain.get(k, 0.0),
            "cover": cover.get(k, 0.0),
            "weight": weight.get(k, 0.0),
        })
    return pd.DataFrame(rows).sort_values("gain", ascending=False)


def get_list_eval(pdxy, cname_tvt='tvt'):
    list_tvt = list(pdxy[cname_tvt].unique())
    i_train = pdxy[cname_tvt] == 'train'
    list_eval = [('train', i_train)]
    for tvt in list_tvt:
        if tvt.startswith('test'):
            i_test = pdxy[cname_tvt] == tvt
            list_eval.append((tvt, i_test))
    i_val = pdxy[cname_tvt] == 'val'
    list_eval.append(('val', i_val))
    return list_eval


def split_tvt(df, cname_tvt='tvt', time_col='time', test_size=10,
              train_cutoff='2022-06-01', val_cutoff='2023-01-01'):
    """
    Split into train/val/test sets.
    - 10% of tickers are assigned to test (any time within test_cutoff range)
    - train: time < train_cutoff
    - val: time in (train_cutoff + 100 days) to val_cutoff
    - test: test tickers in range (train_cutoff, test_cutoff]
    """
    df[time_col] = pd.to_datetime(df[time_col])
    train_cutoff = pd.to_datetime(train_cutoff)
    val_cutoff = pd.to_datetime(val_cutoff)

    list_ticker = list(df['ticker'].unique())
    random.seed(42)
    random.shuffle(list_ticker)

    test_tickers = [list_ticker[i] for i in range(len(list_ticker)) if i % 100 < test_size]

    is_test_ticker = df['ticker'].isin(test_tickers)
    is_train_val_ticker = ~is_test_ticker

    is_train_time = df[time_col] < train_cutoff
    is_cal_time = (df[time_col] >= train_cutoff) & (df[time_col] < val_cutoff)
    is_val_time = (df[time_col] > val_cutoff)

    # Assign tvt
    df[cname_tvt] = 'other'
    df.loc[is_train_val_ticker & is_train_time, cname_tvt] = 'train'
    df.loc[is_train_val_ticker & is_cal_time, cname_tvt] = 'cal'
    df.loc[is_train_val_ticker & is_val_time, cname_tvt] = 'val'
    df.loc[is_test_ticker & is_train_time, cname_tvt] = 'test_1'
    df.loc[is_test_ticker & is_val_time, cname_tvt] = 'test_2'

    print(df[cname_tvt].value_counts())

    return df


def split_tvt_v1(df, time_col='time', cname_tvt='tvt',
                 train_end='2022-06-01',
                 val_end='2022-12-31',
                 test_start='2023-01-01'):
    """
    split_time_only
    """
    df = df.copy()
    df[time_col] = pd.to_datetime(df[time_col])
    train_end = pd.to_datetime(train_end)
    val_end = pd.to_datetime(val_end)
    test_start = pd.to_datetime(test_start)

    df[cname_tvt] = 'other'
    df.loc[df[time_col] <= train_end, cname_tvt] = 'train'
    df.loc[(df[time_col] > train_end) & (df[time_col] <= val_end), cname_tvt] = 'val'
    df.loc[df[time_col] >= test_start, cname_tvt] = 'test'  # 2023+ làm test đúng bài
    return df


def label_strength_4(row, label, thres_2=10, thres_1=0, thres_0=-10):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:3
        - 0: Fail
        - 1: Weak
        - 2: Strong
        - 3: Very Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= thres_2:
        return 3
    elif v >= thres_1:
        return 2
    elif v >= thres_0:
        return 1
    else:
        return 0


def label_strength_3(row, label, strong_th=10, weak_th=0):
    """
    Determine the strength of a label based on profit thresholds.
    Args:
        row (pd.Series): A row from the DataFrame containing profit data.
        label (str): The label to evaluate (e.g., '2W', '1M', '3M').
        strong_th (float): Threshold for strong label.
        weak_th (float): Threshold for weak label.
        fail_th (float): Threshold for fail label.

    Returns:
        str: 0:2
        - 0: Fail
        - 1: Weak
        - 2: Strong

    """
    v = row[f'profit_{label}']
    if v is None:
        return np.nan
    if v >= strong_th:
        return 2
    elif v >= weak_th:
        return 1
    else:
        return 0


def label_binary(row, label, strong_th=5, ceil=True):
    if ceil:
        v = np.ceil(row[f'profit_{label}'])
    else:
        v = row[f'profit_{label}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v1(row, label, strong_th=5, ceil=True):
    center_dict = {
        '2W': 5,
        '1M': 10,
        '3M': 20
    }
    if ceil:
        v = np.ceil(row[f'profit_{label}_center_{center_dict[label]}'])
    else:
        v = row[f'profit_{label}_center_{center_dict[label]}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0


def label_binary_v2(row, label, strong_th=5, ceil=True):
    center_dict = {
        '2W': 3,
        '1M': 7,
        '3M': 15
    }
    if ceil:
        v = np.ceil(row[f'profit_{label}_center_{center_dict[label]}'])
    else:
        v = row[f'profit_{label}_center_{center_dict[label]}']

    if v is None:
        return np.nan
    if v >= strong_th:
        return 1
    else:
        return 0

#%%
def ensure_types(df, labels_tag):
    df = df.copy()
    for c in df.columns:
        if c in labels_tag:
            continue
        if pd.api.types.is_numeric_dtype(df[c]):
            df[c] = df[c].astype(np.float32)
    return df


chunks = []
for chunk in pd.read_csv("deeplearning/dl_train.csv", chunksize=50000):
    chunks.append(chunk)
df_data_all = pd.concat(chunks, ignore_index=True)

# df_data_all = pd.read_parquet(path_parquet)
profit_cols = [col for col in df_data_all.columns if col.startswith('profit_')]
cname_tvt = 'tvt'

df_data_all['week'] = pd.to_datetime(df_data_all['time']).dt.strftime('%Y-%W')
df_data_all['month'] = df_data_all['time'].map(lambda x: x[:7])
labels_tag = [cname_tvt, 'time', 'ticker', 'week', 'month'] + profit_cols
df_data_all = ensure_types(df_data_all, labels_tag)

df_data_all = df_data_all.sort_values(by=['ticker', 'time']).reset_index(drop=True)

# Load data and labels
thres_hold = 10

# for label in ['2W', '1M', '3M']:
for label in ['1M']:
    df_data_all[f'label_binary_{label}_ceil'] = df_data_all.apply(
        lambda row: label_binary(row, label, strong_th=thres_hold),
        axis=1)
    labels_tag.append(f'label_binary_{label}_ceil')

    df_data_all[f'label_binary_{label}_center_v1_ceil'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1_ceil')

    df_data_all[f'label_binary_{label}_center_v2_ceil'] = df_data_all.apply(
        lambda row: label_binary_v2(row, label, strong_th=thres_hold), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2_ceil')

    df_data_all[f'label_binary_{label}'] = df_data_all.apply(
        lambda row: label_binary(row, label, strong_th=thres_hold, ceil=False),
        axis=1)
    labels_tag.append(f'label_binary_{label}')

    df_data_all[f'label_binary_{label}_center_v1'] = df_data_all.apply(
        lambda row: label_binary_v1(row, label, strong_th=thres_hold, ceil=False), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v1')

    df_data_all[f'label_binary_{label}_center_v2'] = df_data_all.apply(
        lambda row: label_binary_v2(row, label, strong_th=thres_hold, ceil=False), axis=1)
    labels_tag.append(f'label_binary_{label}_center_v2')

    # # multi 3
    # df_data_all[f'label_{label}_8'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=8), axis=1)
    # labels_tag.append(f'label_{label}_8')
    # df_data_all[f'label_{label}_12'] = df_data_all.apply(lambda row: label_strength_3(row, label,strong_th=12), axis=1)
    # labels_tag.append(f'label_{label}_12')
    # df_data_all[f'label_{label}_26'] = df_data_all.apply(lambda row: label_strength_3(row, label, strong_th=26), axis=1)
    # labels_tag.append(f'label_{label}_26')
    # #multi 4
    # df_data_all[f'label_{label}_-6_1_9'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=9, thres_1=1, thres_0=-6), axis=1)
    # labels_tag.append(f'label_{label}_-6_1_9')
    # df_data_all[f'label_{label}_-9_2_15'] = df_data_all.apply(lambda row: label_strength_4(row, label,thres_2=15, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-9_2_15')
    # df_data_all[f'label_{label}_-15_7_33'] = df_data_all.apply(lambda row: label_strength_4(row, label, thres_2=33, thres_1=2, thres_0=-9), axis=1)
    # labels_tag.append(f'label_{label}_-15_7_33')


# drops_1 = ['MA20_T1', 'MA200_T1', 'MA10', 'D_RSI_Max1W_MACD', 'D_CMB', 'Close_T1W', 'D_MACDdiff', 'D_CMB_XFast', 'D_RSI/D_RSI_T1', 'D_RSI/D_RSI_T1W']
# labels_tag = labels_tag + drops
# df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first', inplace=True)
#%%
print("Data shape:", df_data_all.shape)
data_is_null = df_data_all.isnull()
mean_null = data_is_null.mean()

drop_cols = []
for col in df_data_all.columns:
    if mean_null[col] > 0.15 and col not in labels_tag:
        drop_cols.append(col)

df_data_all.drop(columns=drop_cols, inplace=True)
print("Data shape:", df_data_all.shape)

#%%
df_data_all = df_data_all.drop(drop_cols, axis=1)
df_data_all = df_data_all.replace([np.inf, -np.inf], np.nan).dropna()

df_data_all = df_data_all.dropna(axis=0, how='any')
df_data_all = split_tvt(df_data_all, test_size=10, time_col='time', train_cutoff='2022-06-01', cal_cutoff='2023-01-01',
                        val_cutoff='2023-06-01')


#%%
df_data = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first').copy()
# df_data = df_data_all.drop_duplicates(subset=['week', 'ticker'], keep='first').copy()
# df_data = df_data_all.copy()
df_data.to_csv('deeplearning/dl_train_used.csv', index=False)
#%%
# # Preprocess data
# N_JOBS = 10
# df_data = df_data_all.dropna(axis=0, how='any').copy()
#
# df_data = df_data.set_index(['ticker', 'time'])
#
# num_cols = df_data.select_dtypes(include=np.number).columns
# for col in ['profit_2W', 'profit_1M', 'profit_3M']:
#     df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]
#
# skew_threshold = 1.0
# log_applied_cols = []
# for col in num_cols:
#     if col in labels_tag:
#         continue
#     skew_val = df_data[col].skew()
#     if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
#         df_data[col] = np.log1p(df_data[col])
#         log_applied_cols.append(col)
#
# for col in num_cols:
#     low = df_data[col].quantile(0.005)
#     high = df_data[col].quantile(0.995)
#     df_data[col] = df_data[col].clip(lower=low, upper=high)
#
# df_data = df_data.sample(frac=1).reset_index(drop=True)
#%%
# Preprocess data
# N_JOBS = 10
#
# df_data = df_data_all.dropna(axis=0, how='any').copy()
#
# df_data = df_data.set_index(['ticker', 'time'])

# df_data = df_data.unstack('ticker').stack('ticker')

# Loại outlier
# for col in ['profit_2W', 'profit_1M', 'profit_3M']:
#     df_data = df_data[(df_data[col] > -90) & (df_data[col] < 200)]

# # 3. Remove cổ phiếu có missing rate > 30%
# threshold = 0.3
# missing_rate = df_data.groupby('ticker').apply(lambda x: x.isna().mean().mean())
# keep_symbols = missing_rate[missing_rate < threshold].index
# df_data = df_data.loc[df_data.index.get_level_values('ticker').isin(keep_symbols)]

# # 6. Remove features tương quan cao >0.95
# num_cols = df_data.select_dtypes(include=np.number).columns
# corr_matrix = df_data[num_cols].corr().abs()
# upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
# to_drop = [column for column in upper.columns if any(upper[column] > 0.95)]
# df_data = df_data.drop(columns=to_drop)
#
# skew_threshold = 2.0
# log_applied_cols = []
# for col in num_cols and col not in labels_tag:
#     skew_val = df_data[col].skew()
#     if abs(skew_val) > skew_threshold and df_data[col].min() >= 0:
#         # df_data[col] = np.log1p(df_data[col])
#         log_applied_cols.append(col)
#
#  # 13. Loại extreme value từng feature (clip 1st-99th percentile)
# for col in num_cols:
#     low = df_data[col].quantile(0.005)
#     high = df_data[col].quantile(0.995)
#     df_data[col] = df_data[col].clip(lower=low, upper=high)

#%% md

#%%
df_data['Volume'].skew()
#%%
# Investigate the dataimport pandas as pd
import matplotlib.pyplot as plt

df = df_data_all.dropna(axis=0, how='any').copy()
df = df_data_all.drop_duplicates(subset=['month', 'ticker'], keep='first')
# 1. Vẽ phân phối profit cho từng time frame
plt.figure(figsize=(15, 5))
for idx, col in enumerate(['profit_2W', 'profit_1M', 'profit_3M']):
    plt.subplot(1, 3, idx + 1)
    plt.hist(df[col], bins=50)
    plt.title(col)
plt.tight_layout()
plt.show()

# 2. Loại outlier (tuỳ thuộc vào chart bên trên, tạm thời cut ở -50% và +50%)
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df = df[(df[col] > -70) & (df[col] < 200)]


# 3. Chia label absolute profit cho từng time frame
def label_profit(x):
    if x < -15:
        return 0
    elif x < 7:
        return 1
    elif x < 33:
        return 2  # lời khá
    else:
        return 3  # lời mạnh


for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    df[f'{col}_class'] = df[col].apply(label_profit)
    print(f"Phân phối label {col}_class:")
    print(df[f'{col}_class'].value_counts(), "\n")

# 4. Chia label theo percentile (quartile) cho từng time frame
for col in ['profit_2W', 'profit_1M', 'profit_3M']:
    # Chạy thử qcut không truyền label để biết số lượng bin thực tế
    try:
        quartile, bins = pd.qcut(df[col], q=2, retbins=True, duplicates='drop')
        num_bin = len(bins) - 1
        df[f'{col}_quartile'] = pd.qcut(df[col], q=2, labels=list(range(num_bin)), duplicates='drop')
        print(f"Phân phối label {col}_quartile:")
        print(df[f'{col}_quartile'].value_counts(), "\n")
        print(bins, "\n")
        # print(quartile)

    except Exception as e:
        print(f"Lỗi chia {col}: {e}")

# # Find insight
# # df_data = df_data_all.dropna(axis=0, how='any')
# label = ['label_2W', 'label_1M', 'label_3M']
# vi_count_unit = (df_data.nunique())
# vi_df_describe = (df_data.describe())
# vi_profit_2W_othernull = (df_data_all[df_data_all['label_2W'].notnull()].isnull().sum())
# vi_profit_1M_othernull = (df_data_all[df_data_all['label_1M'].notnull()].isnull().sum())
# vi_profit_4M_othernull = (df_data_all[df_data_all['label_3M'].notnull()].isnull().sum())
#
# for la in label:
#     # print(df_data_all[f'{la}'].value_counts())
#     print(df_data[f'{la}'].value_counts())
#%%
data_train, data_test1, data_test2, data_cal, data_val = (
    df_data[df_data[cname_tvt] == 'train'].reset_index(drop=True), \
    df_data[df_data[cname_tvt] == 'test1'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'test2'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'cal'].reset_index(drop=True),
    df_data[df_data[cname_tvt] == 'val'].reset_index(drop=True)
)
y_train_m, y_test1_m, y_test2_m, y_cal_m, y_val_m = data_train[labels_tag], data_test1[labels_tag], data_test2[
    labels_tag], data_cal[labels_tag], data_val[labels_tag]

X_train, X_test1, X_test2, X_cal, X_val = (
    data_train.drop(labels_tag, axis=1),
    data_test1.drop(labels_tag, axis=1),
    data_test2.drop(labels_tag, axis=1),
    data_cal.drop(labels_tag, axis=1),
    data_val.drop(labels_tag, axis=1)
)
X_train_final = X_train
X_val_final = X_val
X_cal_final = X_cal
X_test1_final = X_test1
X_test2_final = X_test2

print(X_train_final.shape, X_val_final.shape, X_cal_final.shape, X_test1_final.shape, X_test2_final.shape)


# Normalize
# scaler = StandardScaler()
# scaler.fit(X_train)

# X_train_scaled = scaler.transform(X_train)
# X_cal_scaled = scaler.transform(X_cal)
# X_val_scaled = scaler.transform(X_val)
# X_test1_scaled = scaler.transform(X_test1)
# X_test2_scaled = scaler.transform(X_test2)

# X_train_final = X_train_scaled
# X_val_final = X_val_scaled
# X_cal_final = X_cal_scaled
# X_test1_final = X_test1_scaled
# X_test2_final = X_test2_scaled
#%%
label_list_mc = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2']
for lb in label_list_mc:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_cal = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution before SMOTE")
    plt.show()

    # ======= 2. (Optional) sample_weight cho train set (nên dùng nếu imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)
    scale_pos_weight = sample_weight.max() / sample_weight.min()

    # ======= 3. Định nghĩa param grid cho RandomizedSearchCV =======
    param_dist = {
        'learning_rate': [0.01, 0.03, 0.05, 0.1, 0.15],
        'max_depth': [3, 5, 7],  #độ sâu của cây
        'min_child_weight': [1, 5, 7],  # tối thiểu số lượng sample trong 1 node trước khi split
        'subsample': [0.7, 0.8, 1.0],  # lấy mẫu hàng mỗi cây
        'colsample_bytree': [0.7, 0.8, 1.0],  # lấy mẫu cột mỗi cây
        'gamma': [0, 0.05, 0.1],  # regularization
        'reg_lambda': [2, 4, 6],  # regularization
        'reg_alpha': [0.3, 0.5, 0.9],  # regularization
        'n_estimators': [200, 400, 600],  #số cây tối đa; early stopping sẽ cắt sớm.
        'max_bin': [128, 256, 512],
        # 'early_stopping_rounds': [50, 100, 200]
    }

    xgb_base = xgb.XGBClassifier(
        tree_method='hist',
        objective='binary:logistic',
        n_jobs=-1,
        # eval_metric=['logloss', 'auc', 'error'],
        eval_metric=['auc'],
        random_state=42
    )

    # ======= 4. RandomizedSearchCV =======
    search = RandomizedSearchCV(
        estimator=xgb_base,
        param_distributions=param_dist,
        n_iter=20,  # tăng lên nếu muốn thử nhiều tổ hợp hơn
        scoring='roc_auc',  # hoặc 'roc_auc' nếu muốn tối ưu AUC
        cv=3,
        verbose=2,
        random_state=42,
        n_jobs=-1,
        return_train_score=False
    )
    search.fit(X_train_final, y_train, sample_weight=sample_weight, verbose=False)

    print("\nBest params:", search.best_params_)
    print("Best F1 (CV):", search.best_score_)

    # ======= 5. Train lại model với best params + early stopping =======
    best_params = search.best_params_
    best_params.update({
        'tree_method': 'hist',
        'objective': 'binary:logistic',
        'n_jobs': -1,
        'eval_metric': ['logloss', 'auc', 'error'],
        'random_state': 42,
        'early_stopping_rounds': 40,
    })

    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # ======= Test overfit ============

    y_pred_proba = final_model.predict_proba(X_train_final)[:, 1]
    y_pred = (y_pred_proba >= 0.5).astype(int)

    acc = accuracy_score(y_train, y_pred)
    f1 = f1_score(y_train, y_pred)
    roc = roc_auc_score(y_train, y_pred_proba)
    ks, ks_threshold = ks_score(y_train, y_pred_proba)
    print(f"Test OVERFIT: ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
    print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")
    print(classification_report(y_train, y_pred, digits=4))

    cm = confusion_matrix(y_train, y_pred, normalize='true')
    print(f"Confusion Matrix (normalized) -:\n", cm)

    ###
    base = len(y_train)
    target = y_train.sum()
    percent_target = 100 * target / base

    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{roc * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    report_lift_table(data_train, y_train, y_pred_proba, profit_col=f'profit_{tag}')

    imp = feature_importance_df(final_model)
    joblib.dump(final_model, outdir / "models" / "xgb_model.joblib")
    final_model.get_booster().save_model(str(outdir / "models" / "xgb_model.json"))
    imp.to_csv(outdir / "models" / "feature_importance.csv", index=False)

    print(f"[INFO] Calibrate ({lb})...")
    calibrator = CalibratedClassifierCV(final_model, method="sigmoid", cv="prefit")
    calibrator.fit(X_cal, y_cal)
    joblib.dump(calibrator, outdir / "models" / "calibrator.joblib")

    print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")
    # ======= 6. Đánh giá trên test set =======
    for X_test_final, y_test, data_test, tag in [(X_test1_final, y_test1, data_test1, "TEST1"),
                                                 (X_test2_final, y_test2, data_test2, "TEST2")]:
        # y_pred_proba = calibrator.predict_proba(X_test_final)[:, 1]
        y_pred_proba = final_model.predict_proba(X_test_final)[:, 1]
        y_pred = (y_pred_proba >= 0.5).astype(int)

        acc = accuracy_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)
        roc = roc_auc_score(y_test, y_pred_proba)
        ks, ks_threshold = ks_score(y_test, y_pred_proba)

        print(f"Test {tag}:")
        print(f"ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
        print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")

        print(classification_report(y_test, y_pred, digits=4))

        cm = confusion_matrix(y_test, y_pred, normalize='true')
        print(f"Confusion Matrix (normalized) - {tag}:\n", cm)

        ###
        base = len(y_test)
        target = y_test.sum()
        percent_target = 100 * target / base

        tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
        report = [{
            "Threshold": f"{lb}",
            "Base": f"{base / 1000:.0f}K",
            "Target": f"{target / 1000:.0f}K",
            "%target": f"{percent_target:.0f}%",
            "AUC": f"{roc * 100:.0f}%"
        }]
        report_df = pd.DataFrame(report)
        print(report_df.to_markdown(index=False))
        report_lift_table(data_test, y_test, y_pred_proba, profit_col=f'profit_{tag}')

#%%
label_list_mc = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2', 'label_binary_1M_ceil',
                 'label_binary_1M_center_v1_ceil', 'label_binary_1M_center_v2_ceil']
for lb in label_list_mc:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_cal = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution before SMOTE")
    plt.show()

    # ======= 2. (Optional) sample_weight cho train set (nên dùng nếu imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)
    scale_pos_weight = sample_weight.max() / sample_weight.min()
    # ======= 5. Train lại model với best params + early stopping =======

    # 28/8
    # Week
    best_params = {
        'subsample': 0.6,
        'reg_lambda': 6,
        'reg_alpha': 0.9,
        'n_estimators': 600,
        'min_child_weight': 3,
        'max_depth': 8,
        'max_bin': 256,
        'learning_rate': 0.01,
        'gamma': 0.1,
        'colsample_bytree': 0.8
    }
    # Month
    best_params = {'subsample': 0.7,
                   'reg_lambda': 2,
                   'reg_alpha': 1.1,
                   'n_estimators': 600,
                   'min_child_weight': 5,
                   'max_depth': 8,
                   'max_bin': 128,
                   'learning_rate': 0.01,
                   'gamma': 0,
                   'colsample_bytree': 0.8}
    # best_params = search.best_params_

    best_params.update({
        'tree_method': 'hist',
        'objective': 'binary:logistic',
        'n_jobs': -1,
        'eval_metric': ['logloss', 'auc', 'error'],
        'random_state': 42,
        'early_stopping_rounds': 40,
    })

    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    # ======= Test overfit ============

    y_pred_proba = final_model.predict_proba(X_train_final)[:, 1]
    y_pred = (y_pred_proba >= 0.5).astype(int)

    acc = accuracy_score(y_train, y_pred)
    f1 = f1_score(y_train, y_pred)
    roc = roc_auc_score(y_train, y_pred_proba)
    ks, ks_threshold = ks_score(y_train, y_pred_proba)
    print(f"Test OVERFIT: ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
    print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")
    print(classification_report(y_train, y_pred, digits=4))

    cm = confusion_matrix(y_train, y_pred, normalize='true')
    print(f"Confusion Matrix (normalized) -:\n", cm)

    ###
    base = len(y_train)
    target = y_train.sum()
    percent_target = 100 * target / base

    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{roc * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))

    report_lift_table(data_train, y_train, y_pred_proba, profit_col=f'profit_{tag}')

    imp = feature_importance_df(final_model)
    joblib.dump(final_model, outdir / "models" / "xgb_model.joblib")
    final_model.get_booster().save_model(str(outdir / "models" / "xgb_model.json"))
    imp.to_csv(outdir / "models" / "feature_importance.csv", index=False)

    print(f"[INFO] Calibrate ({lb})...")
    calibrator = CalibratedClassifierCV(final_model, method="sigmoid", cv="prefit")
    calibrator.fit(X_cal, y_cal)
    joblib.dump(calibrator, outdir / "models" / "calibrator.joblib")

    print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")
    # ======= 6. Đánh giá trên test set =======
    for X_test_final, y_test, data_test, tag in [(X_test1_final, y_test1, data_test1, "TEST1"),
                                                 (X_test2_final, y_test2, data_test2, "TEST2")]:
        # y_pred_proba = calibrator.predict_proba(X_test_final)[:, 1]
        y_pred_proba = final_model.predict_proba(X_test_final)[:, 1]

        y_pred = (y_pred_proba >= 0.5).astype(int)

        acc = accuracy_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)
        roc = roc_auc_score(y_test, y_pred_proba)
        ks, ks_threshold = ks_score(y_test, y_pred_proba)

        print(f"Test {tag}:")
        print(f"ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
        print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")

        print(classification_report(y_test, y_pred, digits=4))

        cm = confusion_matrix(y_test, y_pred, normalize='true')
        print(f"Confusion Matrix (normalized) - {tag}:\n", cm)

        ###
        base = len(y_test)
        target = y_test.sum()
        percent_target = 100 * target / base

        tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
        report = [{
            "Threshold": f"{lb}",
            "Base": f"{base / 1000:.0f}K",
            "Target": f"{target / 1000:.0f}K",
            "%target": f"{percent_target:.0f}%",
            "AUC": f"{roc * 100:.0f}%"
        }]
        report_df = pd.DataFrame(report)
        print(report_df.to_markdown(index=False))
        report_lift_table(data_test, y_test, y_pred_proba, profit_col=f'profit_{tag}')

#%%
# --- Warmup: lr cao, model nhẹ để “bắt hình dong” nhanh ---

label_list_mc = ['label_binary_1M_center_v2_ceil']
for lb in label_list_mc:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_cal = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution before SMOTE")
    plt.show()

    # ======= 2. (Optional) sample_weight cho train set (nên dùng nếu imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)
    scale_pos_weight = sample_weight.max() / sample_weight.min()
    # ======= 5. Train lại model với best params + early stopping =======

    # --- Warmup: lr cao, model nhẹ để “bắt hình dong” nhanh ---
    # warm_params = {
    #     # core
    #     'objective': 'binary:logistic',
    #     'eval_metric': 'aucpr',  # bám imbalance tốt hơn
    #     'tree_method': 'hist',
    #     'n_jobs': -1,
    #     'random_state': 42,
    #     'early_stopping_rounds': 40,
    #
    #     # model size
    #     'max_depth': 4,
    #     'min_child_weight': 3,
    #     'subsample': 0.85,
    #     'colsample_bytree': 0.85,
    #     'reg_lambda': 1.0,
    #     'reg_alpha': 0.0,
    #
    #     # warmup lr cao
    #     'learning_rate': 0.20,
    #     'n_estimators': 300,
    #     'gamma': 0.1,
    #     # optional: cân bằng class theo tỷ lệ
    #     # 'scale_pos_weight': float(scale_pos_weight)
    # }

    warm_params = {
        # core
        'objective': 'binary:logistic',
        'eval_metric': 'aucpr',
        'tree_method': 'hist',
        'n_jobs': -1,
        'random_state': 42,
        'early_stopping_rounds': 100,

        # model “nặng” hơn tí
        'max_depth': 8,
        'min_child_weight': 5,
        'subsample': 0.7,
        'colsample_bytree': 0.80,
        'reg_lambda': 2.0,
        'reg_alpha': 1.1,

        # lr base (sẽ bị callback override mỗi round)
        'learning_rate': 0.01,
        'n_estimators': 600,
        'max_bin': 128,
        'gamma': 0,

        # 'scale_pos_weight': float(scale_pos_weight),
    }

    warm_model = xgb.XGBClassifier(**warm_params)
    warm_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False
    )

    final_model = warm_model

#%%
# Refresh leaf

# 1) DMatrix cho refresh (có thể là X_train_final hiện tại hoặc batch data mới)
dtrain = xgb.DMatrix(X_train_final, label=y_train, weight=sample_weight)
dval   = xgb.DMatrix(X_val_final,   label=y_val)

# 2) Refresh leaf: KHÔNG thêm cây mới, chỉ tính lại giá trị lá
refresh_params = {
    # giữ đúng objective/metric như warmup
    "objective": "binary:logistic",
    "eval_metric": "aucpr",

    # magic để refresh
    "process_type": "update",
    "updater": "refresh",
    "refresh_leaf": 1,  # True cũng được
}

refreshed_booster = xgb.train(
    params=refresh_params,
    dtrain=dtrain,
    num_boost_round=0,                   # quan trọng: 0 round => không grow thêm
    evals=[(dval, "val")],
    xgb_model=warm_model.get_booster(),  # nạp booster đã warmup để refresh lá
    verbose_eval=False
)

# 3) Bọc lại về sklearn để dùng predict_proba / calibrator như cũ
tmp_model_path = outdir / "models" / "xgb_refreshed.json"
refreshed_booster.save_model(str(tmp_model_path))

final_model = xgb.XGBClassifier()
final_model.load_model(str(tmp_model_path))

booster = final_model.get_booster()
nrounds = booster.num_boosted_rounds()

# Đồng bộ số round & thuộc tính tối thiểu cho sklearn wrapper
final_model.n_estimators = nrounds
final_model.n_features_in_ = X_train_final.shape[1]
final_model.classes_ = np.array([0, 1])

# 4) (tuỳ chọn) Nếu vẫn muốn thêm một ít cây sau khi refresh:
# final_model2 = xgb.XGBClassifier(
#     objective='binary:logistic', eval_metric='aucpr',
#     tree_method='hist', n_jobs=-1, random_state=42,
#     learning_rate=0.03, n_estimators=150,  # nhẹ thôi
#     max_depth=6, min_child_weight=4, subsample=0.8, colsample_bytree=0.8
# )
# final_model2.fit(
#     X_train_final, y_train,
#     sample_weight=sample_weight,
#     eval_set=[(X_val_final, y_val)],
#     verbose=False,
#     xgb_model=refreshed_booster  # append cây mới từ booster đã refresh
# )
# final_model = final_model2

#%%
 # --- Stage 2: continue training với lr schedule (0.08 -> 0.05) + early stopping ---
label_list_mc = ['label_binary_1M_center_v2_ceil']
for lb in label_list_mc:
    print('total data distribution', df_data[f'{lb}'].value_counts())

    y_train = y_train_m[lb]
    y_val = y_val_m[lb]
    y_cal = y_cal_m[lb]
    y_test1 = y_test1_m[lb]
    y_test2 = y_test2_m[lb]

    print(f"Train {lb} distribution:", np.bincount(y_train))
    sns.countplot(x=y_train)
    plt.title("Label distribution before SMOTE")
    plt.show()

    # ======= 2. (Optional) sample_weight cho train set (nên dùng nếu imbalance) =======
    sample_weight = compute_sample_weight(class_weight='balanced', y=y_train)
    scale_pos_weight = sample_weight.max() / sample_weight.min()
    # ======= 5. Train lại model với best params + early stopping =======

    # --- Stage 2: continue training với lr schedule (0.08 -> 0.05) + early stopping ---
    # def lr_schedule(iter_):
    #     # iter_ tính theo số cây bổ sung ở giai đoạn 2
    #     return 0.05 if iter_ < 200 else 0.01


    # params_stage2_low = {
    #     "objective": "binary:logistic",
    #     "eval_metric": "aucpr",  # metric cho early stopping
    #     "tree_method": "hist",
    #
    #     "max_depth": 8,
    #     "min_child_weight": 5,
    #     "subsample": 0.70,
    #     "colsample_bytree": 0.80,
    #     "lambda": 2.0,
    #     "alpha": 1.1,
    #     "eta": 0.05,  # sẽ bị scheduler override
    #     "nthread": -1,
    #     "seed": 42,
    # }
    # dtrain = xgb.DMatrix(X_train_final, label=y_train, weight=sample_weight)
    # dval = xgb.DMatrix(X_val_final, label=y_val)
    #
    # # Tiếp tục từ booster warmup, KHÔNG dùng sklearn.fit(callbacks=...)
    # booster = xgb.train(
    #     params=params_stage2_low,
    #     dtrain=dtrain,
    #     num_boost_round=600,
    #     evals=[(dtrain, "train"), (dval, "val")],
    #     xgb_model=warm_model.get_booster(),  # append thêm cây
    #     callbacks=[
    #         xgb.callback.EarlyStopping(
    #             rounds=100, save_best=True, maximize=True,
    #             data_name="val", metric_name="aucpr"
    #         ),
    #         xgb.callback.LearningRateScheduler(lr_schedule)
    #     ],
    #     verbose_eval=False
    # )
    #
    # # Bọc lại vào sklearn wrapper để dùng predict_proba / calibrator
    # tmp_model_path = outdir / "models" / "xgb_stage2_booster.json"
    # booster.save_model(str(tmp_model_path))
    #
    # final_model = xgb.XGBClassifier()  # shell rỗng
    # final_model.load_model(str(tmp_model_path))

    stage2_params = {
        # core
        'objective': 'binary:logistic',
        'eval_metric': 'aucpr',
        'tree_method': 'hist',
        'n_jobs': -1,
        'random_state': 42,
        'early_stopping_rounds': 100,

        # model “nặng” hơn tí
        'max_depth': 8,
        'min_child_weight': 5,
        'subsample': 0.7,
        'colsample_bytree': 0.80,
        'reg_lambda': 2.0,
        'reg_alpha': 1.1,

        # lr base (sẽ bị callback override mỗi round)
        'learning_rate': 0.01,
        'n_estimators': 600,
        'max_bin': 128,
        'gamma': 0,

        # 'scale_pos_weight': float(scale_pos_weight),
    }

    final_model = xgb.XGBClassifier(**stage2_params)

    # Tiếp tục từ booster warmup (append thêm cây mới)
    final_model.fit(
        X_train_final, y_train,
        sample_weight=sample_weight,
        eval_set=[(X_val_final, y_val)],
        verbose=False,
        xgb_model=warm_model.get_booster()  # <- tiếp tục từ warmup
    )



#%%

# ======= Test overfit ============

y_pred_proba = final_model.predict_proba(X_train_final)[:, 1]
y_pred = (y_pred_proba >= 0.5).astype(int)

acc = accuracy_score(y_train, y_pred)
f1 = f1_score(y_train, y_pred)
roc = roc_auc_score(y_train, y_pred_proba)
ks, ks_threshold = ks_score(y_train, y_pred_proba)
print(f"Test OVERFIT: ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")
print(classification_report(y_train, y_pred, digits=4))

cm = confusion_matrix(y_train, y_pred, normalize='true')
print(f"Confusion Matrix (normalized) -:\n", cm)

###
base = len(y_train)
target = y_train.sum()
percent_target = 100 * target / base

tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
report = [{
    "Threshold": f"{lb}",
    "Base": f"{base / 1000:.0f}K",
    "Target": f"{target / 1000:.0f}K",
    "%target": f"{percent_target:.0f}%",
    "AUC": f"{roc * 100:.0f}%"
}]
report_df = pd.DataFrame(report)
print(report_df.to_markdown(index=False))

report_lift_table(data_train, y_train, y_pred_proba, profit_col=f'profit_{tag}')

imp = feature_importance_df(final_model)
joblib.dump(final_model, outdir / "models" / "xgb_model.joblib")
final_model.get_booster().save_model(str(outdir / "models" / "xgb_model.json"))
imp.to_csv(outdir / "models" / "feature_importance.csv", index=False)

print(f"[INFO] Calibrate ({lb})...")
calibrator = CalibratedClassifierCV(final_model, method="sigmoid", cv="prefit")
calibrator.fit(X_cal, y_cal)
joblib.dump(calibrator, outdir / "models" / "calibrator.joblib")

print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++")
# ======= 6. Đánh giá trên test set =======
for X_test_final, y_test, data_test, tag in [(X_test1_final, y_test1, data_test1, "TEST1"),
                                             (X_test2_final, y_test2, data_test2, "TEST2")]:
    # y_pred_proba = calibrator.predict_proba(X_test_final)[:, 1]
    y_pred_proba = final_model.predict_proba(X_test_final)[:, 1]

    y_pred = (y_pred_proba >= 0.5).astype(int)

    acc = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    roc = roc_auc_score(y_test, y_pred_proba)
    ks, ks_threshold = ks_score(y_test, y_pred_proba)

    print(f"Test {tag}:")
    print(f"ACC={acc * 100:.2f}% | F1={f1 * 100:.2f}% | ROC={roc * 100:.2f}%")
    print(f"KS_SCORE={ks:.2f} | KS_THRESHOLD={ks_threshold:.2f}")

    print(classification_report(y_test, y_pred, digits=4))

    cm = confusion_matrix(y_test, y_pred, normalize='true')
    print(f"Confusion Matrix (normalized) - {tag}:\n", cm)

    ###
    base = len(y_test)
    target = y_test.sum()
    percent_target = 100 * target / base

    tag = '2M' if '2M' in lb else '3M' if '3M' in lb else '1M'
    report = [{
        "Threshold": f"{lb}",
        "Base": f"{base / 1000:.0f}K",
        "Target": f"{target / 1000:.0f}K",
        "%target": f"{percent_target:.0f}%",
        "AUC": f"{roc * 100:.0f}%"
    }]
    report_df = pd.DataFrame(report)
    print(report_df.to_markdown(index=False))
    report_lift_table(data_test, y_test, y_pred_proba, profit_col=f'profit_{tag}')
